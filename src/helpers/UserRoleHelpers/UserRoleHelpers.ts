import { UserRoleType } from '@/interface-models/Roles/UserRoles';
import { sessionManager } from '@/store/session/SessionState';

export const hasAdminOrTeamLeaderOrBranchManagerRole = (): boolean => {
  return sessionManager
    .getRoles()
    .some(
      (r) =>
        r === UserRoleType.ROLE_ADMIN ||
        r === UserRoleType.ROLE_TEAM_LEADER ||
        r === UserRoleType.ROLE_BRANCH_MANAGER,
    );
};

export const hasAdminRole = (): boolean => {
  return sessionManager.getRoles().some((r) => r === UserRoleType.ROLE_ADMIN);
};

export const hasAdminOrHeadOfficeRole = (): boolean => {
  return sessionManager
    .getRoles()
    .some(
      (r) =>
        r === UserRoleType.ROLE_ADMIN || r === UserRoleType.ROLE_HEAD_OFFICE,
    );
};
export const hasAdminOrHeadOfficeOrBranchManagerRole = (): boolean => {
  return sessionManager
    .getRoles()
    .some(
      (r) =>
        r === UserRoleType.ROLE_ADMIN ||
        r === UserRoleType.ROLE_HEAD_OFFICE ||
        r === UserRoleType.ROLE_BRANCH_MANAGER,
    );
};

export const hasAdminOrHeadOfficeOrTeamLeaderOrBranchManagerRole =
  (): boolean => {
    return sessionManager
      .getRoles()
      .some(
        (r) =>
          r === UserRoleType.ROLE_ADMIN ||
          r === UserRoleType.ROLE_HEAD_OFFICE ||
          r === UserRoleType.ROLE_TEAM_LEADER ||
          r === UserRoleType.ROLE_BRANCH_MANAGER,
      );
  };

export const hasAdminOrCsrOrTeamLeaderOrBranchManagerRole = (): boolean => {
  return sessionManager
    .getRoles()
    .some(
      (r) =>
        r === UserRoleType.ROLE_ADMIN ||
        r === UserRoleType.ROLE_CSR ||
        r === UserRoleType.ROLE_TEAM_LEADER ||
        r === UserRoleType.ROLE_BRANCH_MANAGER,
    );
};

export const hasAdminOrCsrOrHeadOfficeOrTeamLeaderOrBranchManagerRole =
  (): boolean => {
    return sessionManager
      .getRoles()
      .some(
        (r) =>
          r === UserRoleType.ROLE_ADMIN ||
          r === UserRoleType.ROLE_CSR ||
          r === UserRoleType.ROLE_HEAD_OFFICE ||
          r === UserRoleType.ROLE_TEAM_LEADER ||
          r === UserRoleType.ROLE_BRANCH_MANAGER,
      );
  };

export const hasAccountRole = (): boolean => {
  return sessionManager
    .getRoles()
    .some((r) => r === UserRoleType.ROLE_ACCOUNTS);
};

export const hasAccountManagementRole = (): boolean => {
  return sessionManager
    .getRoles()
    .some((r) => r === UserRoleType.ROLE_ACCOUNTS_MANAGEMENT);
};

/**
 * Checks if the current user has read-only access to user management
 * CSR and TEAM_LEADER roles have read-only access when they don't have higher privileges
 */
export const isReadOnlyUser = (): boolean => {
  const userRoles = sessionManager.getRoles();
  return (
    userRoles.some(
      (r) => r === UserRoleType.ROLE_CSR || r === UserRoleType.ROLE_TEAM_LEADER,
    ) &&
    !userRoles.some(
      (r) =>
        r === UserRoleType.ROLE_ADMIN ||
        r === UserRoleType.ROLE_HEAD_OFFICE ||
        r === UserRoleType.ROLE_BRANCH_MANAGER,
    )
  );
};

/**
 * Checks if the current user can manage a specific role type
 * @param roleType - The role type to check permissions for
 */
export const canManageSpecificRole = (roleType: UserRoleType): boolean => {
  // Admin can manage all roles
  if (hasAdminRole()) {
    return true;
  }

  // Read-only users cannot manage any roles
  if (isReadOnlyUser()) {
    return false;
  }

  // HEAD_OFFICE and BRANCH_MANAGER can only manage CSR and TEAM_LEADER roles
  if (hasAdminOrHeadOfficeOrBranchManagerRole()) {
    return (
      roleType === UserRoleType.ROLE_CSR ||
      roleType === UserRoleType.ROLE_TEAM_LEADER
    );
  }

  return false;
};
