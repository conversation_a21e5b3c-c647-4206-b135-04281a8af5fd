import { UserRoleType } from '@/interface-models/Roles/UserRoles';
import { sessionManager } from '@/store/session/SessionState';

export const hasAdminOrTeamLeaderOrBranchManagerRole = (): boolean => {
  return sessionManager
    .getRoles()
    .some(
      (r) =>
        r === UserRoleType.ROLE_ADMIN ||
        r === UserRoleType.ROLE_TEAM_LEADER ||
        r === UserRoleType.ROLE_BRANCH_MANAGER,
    );
};

export const hasAdminRole = (): boolean => {
  return sessionManager.getRoles().some((r) => r === UserRoleType.ROLE_ADMIN);
};

export const hasAdminOrHeadOfficeRole = (): boolean => {
  return sessionManager
    .getRoles()
    .some(
      (r) =>
        r === UserRoleType.ROLE_ADMIN || r === UserRoleType.ROLE_HEAD_OFFICE,
    );
};
export const hasAdminOrHeadOfficeOrBranchManagerRole = (): boolean => {
  return sessionManager
    .getRoles()
    .some(
      (r) =>
        r === UserRoleType.ROLE_ADMIN ||
        r === UserRoleType.ROLE_HEAD_OFFICE ||
        r === UserRoleType.ROLE_BRANCH_MANAGER,
    );
};

export const hasAdminOrHeadOfficeOrTeamLeaderOrBranchManagerRole =
  (): boolean => {
    return sessionManager
      .getRoles()
      .some(
        (r) =>
          r === UserRoleType.ROLE_ADMIN ||
          r === UserRoleType.ROLE_HEAD_OFFICE ||
          r === UserRoleType.ROLE_TEAM_LEADER ||
          r === UserRoleType.ROLE_BRANCH_MANAGER,
      );
  };

export const hasAdminOrCsrOrTeamLeaderOrBranchManagerRole = (): boolean => {
  return sessionManager
    .getRoles()
    .some(
      (r) =>
        r === UserRoleType.ROLE_ADMIN ||
        r === UserRoleType.ROLE_CSR ||
        r === UserRoleType.ROLE_TEAM_LEADER ||
        r === UserRoleType.ROLE_BRANCH_MANAGER,
    );
};

export const hasAdminOrCsrOrHeadOfficeOrTeamLeaderOrBranchManagerRole =
  (): boolean => {
    return sessionManager
      .getRoles()
      .some(
        (r) =>
          r === UserRoleType.ROLE_ADMIN ||
          r === UserRoleType.ROLE_CSR ||
          r === UserRoleType.ROLE_HEAD_OFFICE ||
          r === UserRoleType.ROLE_TEAM_LEADER ||
          r === UserRoleType.ROLE_BRANCH_MANAGER,
      );
  };

export const hasAccountRole = (): boolean => {
  return sessionManager
    .getRoles()
    .some((r) => r === UserRoleType.ROLE_ACCOUNTS);
};

export const hasAccountManagementRole = (): boolean => {
  return sessionManager
    .getRoles()
    .some((r) => r === UserRoleType.ROLE_ACCOUNTS_MANAGEMENT);
};

/**
 * Checks if the current user can manage admin roles (assign/remove ROLE_ADMIN)
 * Only users with ROLE_ADMIN can manage admin roles
 */
export const canManageAdminRole = (): boolean => {
  return hasAdminRole();
};

/**
 * Checks if the current user has read-only access to user management
 * CSR and TEAM_LEADER roles have read-only access
 */
export const isReadOnlyUser = (): boolean => {
  const userRoles = sessionManager.getRoles();
  return (
    userRoles.some(
      (r) => r === UserRoleType.ROLE_CSR || r === UserRoleType.ROLE_TEAM_LEADER,
    ) &&
    !userRoles.some(
      (r) =>
        r === UserRoleType.ROLE_ADMIN ||
        r === UserRoleType.ROLE_HEAD_OFFICE ||
        r === UserRoleType.ROLE_BRANCH_MANAGER,
    )
  );
};

/**
 * Checks if the current user can manage user roles (not read-only)
 * Excludes CSR and TEAM_LEADER only users
 */
export const canManageUserRoles = (): boolean => {
  return !isReadOnlyUser();
};

/**
 * Checks if the current user can manage a specific role type
 * @param roleType - The role type to check permissions for
 */
export const canManageSpecificRole = (roleType: UserRoleType): boolean => {
  const userRoles = sessionManager.getRoles();

  // Admin can manage all roles
  if (userRoles.includes(UserRoleType.ROLE_ADMIN)) {
    return true;
  }

  // Read-only users cannot manage any roles
  if (isReadOnlyUser()) {
    return false;
  }

  // HEAD_OFFICE and BRANCH_MANAGER can only manage CSR and TEAM_LEADER roles
  if (
    userRoles.some(
      (r) =>
        r === UserRoleType.ROLE_HEAD_OFFICE ||
        r === UserRoleType.ROLE_BRANCH_MANAGER,
    )
  ) {
    return (
      roleType === UserRoleType.ROLE_CSR ||
      roleType === UserRoleType.ROLE_TEAM_LEADER
    );
  }

  return false;
};

/**
 * Checks if the current user can remove a user who has specific roles
 * @param userRoleTypes - Array of role types the target user has
 */
export const canRemoveUserWithRoles = (
  userRoleTypes: UserRoleType[],
): boolean => {
  const currentUserRoles = sessionManager.getRoles();

  // Admin can remove any user
  if (currentUserRoles.includes(UserRoleType.ROLE_ADMIN)) {
    return true;
  }

  // Read-only users cannot remove any users
  if (isReadOnlyUser()) {
    return false;
  }

  // HEAD_OFFICE and BRANCH_MANAGER can only remove users who have ONLY CSR or TEAM_LEADER roles
  if (
    currentUserRoles.some(
      (r) =>
        r === UserRoleType.ROLE_HEAD_OFFICE ||
        r === UserRoleType.ROLE_BRANCH_MANAGER,
    )
  ) {
    return userRoleTypes.every(
      (roleType) =>
        roleType === UserRoleType.ROLE_CSR ||
        roleType === UserRoleType.ROLE_TEAM_LEADER,
    );
  }

  return false;
};
