<template>
  <div class="user-account-management">
    <v-flex md12>
      <TableTitleHeader>
        <!-- Title slot -->
        <template #title>
          <GTitle
            title="Employees"
            subtitle="Manage employee details and access"
            :divider="false"
          />
        </template>

        <!-- Inputs slot -->
        <template #buttons>
          <GButton
            :iconRight="true"
            @click="addNewUser"
            :disabled="
              !hasAdminOrHeadOfficeRole() || companyUserWithAuthDetails !== null
            "
            ><v-icon size="12" color="white">fa-user-plus</v-icon> Create New
            User</GButton
          >
        </template>
      </TableTitleHeader>

      <CompanyUserTable @selectUser="viewUser" />

      <GDialog
        v-if="companyUserWithAuthDetails"
        :width="'750px'"
        :title="'Company User Details'"
        :confirmBtnText="'Save'"
        :confirmDisabled="false"
        @closeDialog="cancelMaintenance"
        @confirm="saveUser"
        :isLoading="
          isMobileVerificationLoading ||
          isEmailVerificationLoading ||
          isSaveCompanyUserLoading
        "
      >
        <CompanyUserMaintenance
          ref="companyUserMaintenanceRef"
          v-if="companyUserWithAuthDetails"
          :companyUserWithAuthDetails="companyUserWithAuthDetails"
          :companyDivisionList="companyDivisionList"
          @cancelMaintenance="cancelMaintenance"
          @setIsEmailVerificationLoading="setIsEmailVerificationLoading"
          @setIsMobileVerificationLoading="setIsMobileVerificationLoading"
        >
        </CompanyUserMaintenance>
      </GDialog>
    </v-flex>
  </div>
</template>

<script setup lang="ts">
import CompanyUserMaintenance from '@/components/admin/Administration/company_user_management/company_user_maintenance.vue';
import CompanyUserTable from '@/components/admin/Administration/company_user_management/company_user_table.vue';
import TableTitleHeader from '@/components/common/ui-elements/table_title_header.vue';
import { showNotification } from '@/helpers/NotificationHelpers/NotificationHelpers';
import { hasAdminOrHeadOfficeRole } from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import { validate } from '@/helpers/ValidationHelpers/ValidationHelpers';
import {
  EditUsernameForUserRequest,
  EditUsernameForUserResponse,
  RoleUserType,
} from '@/interface-models/Email/EmailAlertRecipients/EditUsernameForUser.ts';
import { KeyValue } from '@/interface-models/Generic/KeyValue/KeyValue';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import {
  AddRoleReq,
  CompanyRoleStatus,
  CompanyUserWithAuthDetails,
  SaveNewCompanyUserDetailsRequest,
} from '@/interface-models/User/CompanyUserDetails';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import { useUserManagementStore } from '@/store/modules/UserManagementStore';
import { computed, ComputedRef, ref, Ref } from 'vue';

// The selected/new company user. Also acts as a conditional on whether the dialog is open or not.
const companyUserWithAuthDetails: Ref<CompanyUserWithAuthDetails | null> =
  ref(null);
const isEmailVerificationLoading: Ref<boolean> = ref(false);
const isMobileVerificationLoading: Ref<boolean> = ref(false);
const isSaveCompanyUserLoading: Ref<boolean> = ref(false);
const userManagementStore = useUserManagementStore();
// A ref to the company user maintenance component. Utilised in data validation before the save request.
const companyUserMaintenanceRef = ref(null);
const initialEmail = ref<string | null>(null);
const initialMobile = ref<string | null>(null);

// A list of the companies divisions. Utilised in the division filtering select
const companyDivisionList: ComputedRef<KeyValue[]> = computed(() => {
  return useCompanyDetailsStore().companyDivisionNamesList ?? [];
});

/**
 * Cancel user maintenance component/dialog
 */
function cancelMaintenance(): void {
  initialEmail.value = null;
  initialMobile.value = null;
  companyUserWithAuthDetails.value = null;
}

/**
 * Emitted event from child component. Sets the loading state when requesting to validate existence of a email address.
 */
function setIsEmailVerificationLoading(isLoading: boolean) {
  isEmailVerificationLoading.value = isLoading;
}

/**
 * Emitted event from child component. Sets the loading state when requesting to validate existence of a mobile number.
 */
function setIsMobileVerificationLoading(isLoading: boolean) {
  isMobileVerificationLoading.value = isLoading;
}

/**
 * Sets the selected user and opens the user maintenance dialog.
 *   @param {CompanyUserWithAuthDetails} selectedUser - The selected user
 */
function viewUser(selectedUser: CompanyUserWithAuthDetails): void {
  selectedUser.companyUser.contactNumber =
    selectedUser.companyUser.contactNumber.replaceAll(/\s/g, '');
  companyUserWithAuthDetails.value = JSON.parse(JSON.stringify(selectedUser));
  setInitialUsernameValues(companyUserWithAuthDetails.value);
}

/**
 * Sets a new company user and opens the maintenance dialog.
 */
function addNewUser(): void {
  companyUserWithAuthDetails.value = new CompanyUserWithAuthDetails();
  setInitialUsernameValues(companyUserWithAuthDetails.value);
}

/**
 * Saves a new users. Because the company user data is not provided in the same form as the save request we are required to transforms CompanyUserWithAuthDetails into SaveNewCompanyUserDetailsRequest.
 */
async function saveUser() {
  if (
    isNewCompanyUser.value &&
    Array.isArray(companyUserWithAuthDetails.value?.companyRoles) &&
    companyUserWithAuthDetails.value.companyRoles.length === 0
  ) {
    if (!validate(companyUserMaintenanceRef.value)) {
      return;
    }
  }
  if (!companyUserWithAuthDetails.value) {
    return;
  }

  const rolesToAdd: AddRoleReq[] = [];
  if (isNewCompanyUser.value) {
    const divisionIds: string[] = [
      ...new Set(
        companyUserWithAuthDetails.value.companyRoles.map(
          (x: CompanyRoleStatus) => x.division,
        ),
      ),
    ];
    for (const divisionId of divisionIds) {
      const divisionRoles =
        companyUserWithAuthDetails.value.companyRoles.filter(
          (x: CompanyRoleStatus) => x.division === divisionId,
        );
      const divisionRolesToAdd: AddRoleReq = {
        division: divisionId,
        roleIds: divisionRoles.map((x: CompanyRoleStatus) => x.roleId),
      };
      rolesToAdd.push(divisionRolesToAdd);
    }
  }
  isSaveCompanyUserLoading.value = true;
  const saveNewCompanyUserDetailsRequest: SaveNewCompanyUserDetailsRequest = {
    companyUserDetails: companyUserWithAuthDetails.value.companyUser,
    rolesToAdd,
  };

  // If the email or mobile number has changed, send an edit username request
  // to update the user's email and mobile number.
  const isEmailChanged =
    !!initialEmail.value &&
    initialEmail.value !==
      companyUserWithAuthDetails.value.companyUser.emailAddress;
  const isMobileChanged =
    !!initialMobile.value &&
    initialMobile.value !==
      companyUserWithAuthDetails.value.companyUser.contactNumber;

  // If the email or mobile has changed, we need to send an edit username request
  if (isEmailChanged || isMobileChanged) {
    const success = await sendEditUsernameRequest();
    // If the request failed, show an error notification and revert the email
    // and mobile
    if (!success) {
      showNotification(
        'An error occurred while updating the company user contact details.',
        {
          title: 'Company User Maintenance',
          type: HealthLevel.ERROR,
        },
      );
      companyUserWithAuthDetails.value.companyUser.emailAddress =
        initialEmail.value ?? '';
      companyUserWithAuthDetails.value.companyUser.contactNumber =
        initialMobile.value ?? '';
      isSaveCompanyUserLoading.value = false;
      return;
    }
  }
  // Send request to save the company user
  const companyUserWithAuthDetailsResponse =
    await userManagementStore.saveNewCompanyUser(
      saveNewCompanyUserDetailsRequest,
    );
  showSaveNotification(companyUserWithAuthDetailsResponse);
  if (companyUserWithAuthDetailsResponse) {
    companyUserWithAuthDetails.value = null;
  }
  isSaveCompanyUserLoading.value = false;
}

/**
 * Displays a notification based on the result of saving a company user.
 *
 * @param {CompanyUserWithAuthDetails | null} response - The response from the
 * save operation. If the response is not null, the save operation was
 * successful.
 */
function showSaveNotification(response: CompanyUserWithAuthDetails | null) {
  // Determine the notification message and type based on the response
  const notificationMessage = response
    ? `Company user successfully ${
        companyUserWithAuthDetails.value?.companyUser._id ? 'saved' : 'created'
      }.`
    : 'An error occurred while saving the company user.';
  const notificationType = response ? HealthLevel.INFO : HealthLevel.ERROR;

  // Show the notification
  showNotification(notificationMessage, {
    title: 'Company User Maintenance',
    type: notificationType,
  });
}

/**
 * Send can editUserName request. Used for checking if user email is changed to resend invite.
 */
async function sendEditUsernameRequest(): Promise<boolean> {
  if (!companyUserWithAuthDetails.value?.companyUser.authRefId) {
    return false;
  }
  const payload: EditUsernameForUserRequest = {
    authRefId: companyUserWithAuthDetails.value.companyUser.authRefId,
    newEmailAddress: companyUserWithAuthDetails.value.companyUser.emailAddress,
    newContactNumber:
      companyUserWithAuthDetails.value.companyUser.contactNumber,
    name: companyUserWithAuthDetails.value.companyUser.firstName,
    userType: RoleUserType.COMPANY,
    clientId: null,
  };
  const response: EditUsernameForUserResponse | null =
    await userManagementStore.editUsernameAndResendInvite(payload);

  return response ? response.editSuccessful : false;
}

/**
 * Returns a boolean for if the user is a new user or not. dictated by the mongo _id
 * @return {Boolean}
 */
const isNewCompanyUser: ComputedRef<boolean> = computed(() => {
  if (!companyUserWithAuthDetails.value) {
    return false;
  }
  return !companyUserWithAuthDetails.value.companyUser._id;
});

/**
 * Set the initial email and mobile values for the user, so we can check if the
 * email or mobile has changed when saving the user. This is called when the
 * maintenance dialog is opened.
 */
function setInitialUsernameValues(
  user: CompanyUserWithAuthDetails | null,
): void {
  if (!user?.companyUser?.emailAddress) {
    return;
  }
  initialEmail.value = user.companyUser.emailAddress;
  initialMobile.value = user?.companyUser?.contactNumber;
}
</script>

<style scoped lang="scss"></style>
